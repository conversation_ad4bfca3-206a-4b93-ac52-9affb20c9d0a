import React from 'react';
import { View, StyleSheet, TouchableOpacity, ActivityIndicator, ScrollView } from 'react-native';
import { useLocalSearchParams, router } from 'expo-router';
import { useTheme } from '@/context/ThemeContext';
import { ThemedText } from '@/components/base/ThemedText';
import { Icon } from '@/components/images/Icon';
import { CommentList } from '@/components/feature/CommentList';
import { BlinkCard } from '@/components/feature/BlinkCard';
import { useTranslation } from 'react-i18next';
import { LinearGradient } from 'expo-linear-gradient';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { useBlinkInterface } from '@/hooks/interfaces/useBlinkInterface';

// Fonction pour transformer les données du blink avec les données du profil
function transformBlinkData(blink: any, profileData: any) {
  console.log('=== TRANSFORM BLINK DATA ===');
  console.log('Input blink:', JSON.stringify(blink, null, 2));
  console.log('Input profile:', JSON.stringify(profileData, null, 2));

  if (!blink) {
    console.log('Blink is null/undefined, returning as is');
    return blink;
  }

  // Si le blink a déjà la structure correcte, le retourner tel quel
  if (blink.profile) {
    console.log('Blink already has profile, returning as is');
    return blink;
  }

  // Si nous avons des données de profil, les utiliser
  if (profileData?.data) {
    console.log('Using profile data to create blink.profile');
    const profile = profileData.data;
    const avatarUrl = profile.avatar_url
      ? `${process.env.EXPO_PUBLIC_API_URL}/uploads/${profile.avatar_url}`
      : `${process.env.EXPO_PUBLIC_API_URL}/uploads/default_user.png`;

    const transformed = {
      ...blink,
      profile: {
        userID: profile.userID,
        username: profile.username,
        display_name: profile.display_name,
        avatar_url: avatarUrl,
      }
    };

    console.log('Transformed blink with profile:', JSON.stringify(transformed, null, 2));
    return transformed;
  }

  // Cas 1: blink.user.Profile (comme dans les commentaires)
  if (blink.user && blink.user.Profile) {
    console.log('Transforming blink.user.Profile to blink.profile');
    const avatarUrl = blink.user.Profile.avatar_url
      ? `${process.env.EXPO_PUBLIC_API_URL}/uploads/${blink.user.Profile.avatar_url}`
      : `${process.env.EXPO_PUBLIC_API_URL}/uploads/default_user.png`;

    const transformed = {
      ...blink,
      profile: {
        userID: blink.user.userID,
        username: blink.user.Profile.username,
        display_name: blink.user.Profile.display_name,
        avatar_url: avatarUrl,
      }
    };

    console.log('Transformed blink:', JSON.stringify(transformed, null, 2));
    return transformed;
  }

  // Cas 2: blink.Profile (directement au niveau du blink)
  if (blink.Profile) {
    console.log('Transforming blink.Profile to blink.profile');
    const avatarUrl = blink.Profile.avatar_url
      ? `${process.env.EXPO_PUBLIC_API_URL}/uploads/${blink.Profile.avatar_url}`
      : `${process.env.EXPO_PUBLIC_API_URL}/uploads/default_user.png`;

    const transformed = {
      ...blink,
      profile: {
        userID: blink.userID,
        username: blink.Profile.username,
        display_name: blink.Profile.display_name,
        avatar_url: avatarUrl,
      }
    };

    console.log('Transformed blink:', JSON.stringify(transformed, null, 2));
    return transformed;
  }

  // Cas 3: blink.User (avec un U majuscule)
  if (blink.User) {
    console.log('Transforming blink.User to blink.profile');
    const avatarUrl = blink.User.avatar_url
      ? `${process.env.EXPO_PUBLIC_API_URL}/uploads/${blink.User.avatar_url}`
      : `${process.env.EXPO_PUBLIC_API_URL}/uploads/default_user.png`;

    const transformed = {
      ...blink,
      profile: {
        userID: blink.User.userID || blink.userID,
        username: blink.User.username,
        display_name: blink.User.display_name,
        avatar_url: avatarUrl,
      }
    };

    console.log('Transformed blink:', JSON.stringify(transformed, null, 2));
    return transformed;
  }

  // Créer un profil par défaut si aucune structure n'est trouvée
  console.log('No profile structure found, creating default profile');
  const defaultProfile = {
    ...blink,
    profile: {
      userID: blink.userID || 'unknown',
      username: 'unknown',
      display_name: 'Utilisateur inconnu',
      avatar_url: `${process.env.EXPO_PUBLIC_API_URL}/uploads/default_user.png`,
    }
  };

  console.log('Default profile created:', JSON.stringify(defaultProfile, null, 2));
  return defaultProfile;
}

export default function CommentsPage() {
  const { blinkID } = useLocalSearchParams<{ blinkID: string }>();
  const { colors } = useTheme();
  const { t } = useTranslation();
  const insets = useSafeAreaInsets();

  // Récupérer les données du blink
  const { data: blinkData, isLoading: isBlinkLoading, isError: isBlinkError } = useBlinkInterface(blinkID || '');

  // Récupérer le profil de l'utilisateur du blink
  const { data: profileData, isLoading: isProfileLoading, isError: isProfileError } = useUserProfileQuery(
    blinkData?.data?.userID || '',
    ['blink-profile']
  );

  // Debug: Log pour voir la structure des données
  console.log('Blink data from API:', blinkData);
  console.log('Profile data from API:', profileData);

  if (!blinkID) {
    return (
      <View style={[styles.container, { backgroundColor: colors.background }]}>
        <ThemedText style={[styles.errorText, { color: colors.danger }]}>
          {t('comment.invalidBlink')}
        </ThemedText>
      </View>
    );
  }

  const handleGoBack = () => {
    if (router.canGoBack()) {
      router.back();
    } else {
      router.push('/');
    }
  };

  return (
    <View style={[styles.container, { backgroundColor: colors.background }]}>
      {/* Header simple avec bouton retour */}
      <View style={[styles.simpleHeader, { paddingTop: insets.top, backgroundColor: colors.background }]}>
        <TouchableOpacity style={styles.backButton} onPress={handleGoBack}>
          <Icon name="arrow-left" size={24} color={colors.text} />
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.scrollContainer} showsVerticalScrollIndicator={false}>
        {/* Affichage du blink */}
        {isBlinkLoading || isProfileLoading ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color={colors.accent} />
            <ThemedText style={[styles.loadingText, { color: colors.textSecondary }]}>
              Chargement du blink...
            </ThemedText>
          </View>
        ) : isBlinkError || isProfileError || !blinkData?.data ? (
          <View style={styles.errorContainer}>
            <ThemedText style={[styles.errorText, { color: colors.danger }]}>
              Erreur lors du chargement du blink
            </ThemedText>
          </View>
        ) : (
          <View style={styles.blinkContainer}>
            {(() => {
              const transformedBlink = transformBlinkData(blinkData.data, profileData);
              console.log('About to render BlinkCard with:', transformedBlink);

              // Vérification de sécurité
              if (!transformedBlink || !transformedBlink.profile) {
                return (
                  <View style={styles.errorContainer}>
                    <ThemedText style={[styles.errorText, { color: colors.danger }]}>
                      Erreur: Structure de données du blink invalide
                    </ThemedText>
                  </View>
                );
              }

              return <BlinkCard blink={transformedBlink} onExpire={() => {
                // Gérer l'expiration du blink si nécessaire
                console.log('Blink expired:', transformedBlink.blinkID);
              }} />;
            })()}
          </View>
        )}

        {/* Liste des commentaires */}
        <CommentList blinkID={blinkID} showInput={true} />
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  simpleHeader: {
    paddingHorizontal: 16,
    paddingBottom: 8,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.1)',
  },
  backButton: {
    padding: 8,
    alignSelf: 'flex-start',
  },
  scrollContainer: {
    flex: 1,
  },
  blinkContainer: {
    paddingHorizontal: 16,
    paddingTop: 8,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 50,
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 50,
  },
  errorText: {
    fontSize: 16,
    textAlign: 'center',
  },
});
